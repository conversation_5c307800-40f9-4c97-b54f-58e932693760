import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/utils/logger.dart';

enum GoDetailsStep {
  initial,
  afterValidation,
  afterLocalObjectives,
}

class GoDetailsProvider extends ChangeNotifier {
  // Controllers
  final TextEditingController solutionController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController localObjectiveController = TextEditingController();

  // State variables
  GoDetailsStep _currentStep = GoDetailsStep.initial;
  PostgresRole? _selectedRole;
  bool _isValidating = false;
  String? _validationError;

  // Mock data for demonstration - in real app this would come from API
  String? _generatedDescription;
  List<String> _localObjectives = [];

  // Pathway creation state
  Map<int, bool> _pathwayCreationStates = {}; // Track which LOs have pathway creation open
  Map<int, PostgresRole?> _pathwaySelectedRoles = {}; // Track selected roles for each LO
  Map<int, String?> _pathwaySelectedTypes = {}; // Track selected types for each LO
  Map<int, String?> _pathwaySelectedLOs = {}; // Track selected LOs for sequential type

  // LO insertion state
  final Map<int, bool> _loInsertionStates = {}; // Track which LOs have insertion text field open
  final Map<int, TextEditingController> _loInsertionControllers = {}; // Controllers for insertion text fields

  // Getters
  GoDetailsStep get currentStep => _currentStep;
  PostgresRole? get selectedRole => _selectedRole;
  bool get isValidating => _isValidating;
  String? get validationError => _validationError;
  String? get generatedDescription => _generatedDescription;
  List<String> get localObjectives => _localObjectives;

  // Pathway creation getters
  bool isPathwayCreationOpen(int loIndex) => _pathwayCreationStates[loIndex] ?? false;
  PostgresRole? getPathwaySelectedRole(int loIndex) => _pathwaySelectedRoles[loIndex];
  String? getPathwaySelectedType(int loIndex) => _pathwaySelectedTypes[loIndex];
  String? getPathwaySelectedLO(int loIndex) => _pathwaySelectedLOs[loIndex];

  // LO insertion getters
  bool isLoInsertionOpen(int loIndex) => _loInsertionStates[loIndex] ?? false;
  TextEditingController? getLoInsertionController(int loIndex) => _loInsertionControllers[loIndex];

  // Get available LOs for sequential selection (LOs after the current one)
  List<String> getAvailableLOsForSequential(int currentLoIndex) {
    if (currentLoIndex >= _localObjectives.length - 1) return [];
    return _localObjectives
        .asMap()
        .entries
        .where((entry) => entry.key > currentLoIndex)
        .map((entry) => entry.value) // Return only the LO name without prefix
        .toList();
  }

  /// Sets the selected role
  void setSelectedRole(PostgresRole? role) {
    if (_selectedRole != role) {
      _selectedRole = role;
      Logger.info('GoDetailsProvider: Selected role changed to: ${role?.name ?? 'None'}');
      notifyListeners();
    }
  }

  /// Validates the solution and moves to next step
  Future<void> validateSolution() async {
    if (solutionController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info('GoDetailsProvider: Validating solution: ${solutionController.text}');

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 2));

      // Mock response - in real app this would come from API
      _generateMockResponse();

      _currentStep = GoDetailsStep.afterValidation;
      Logger.info('GoDetailsProvider: Solution validated successfully');

    } catch (e) {
      Logger.error('GoDetailsProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
  }

  /// Generates mock response for demonstration
  void _generateMockResponse() {
    final solutionText = solutionController.text.trim();
    
    // Generate description based on solution
    _generatedDescription = 'Comprehensive ${solutionText.toLowerCase()} process from registration to welcome completion';
    descriptionController.text = _generatedDescription ?? '';
    
    // Generate mock local objectives
    _localObjectives = [
      'Type LO name with full stop (.)',
    ];
    
    notifyListeners();
  }

  /// Processes local objectives from text input
  void processLocalObjectives() {
    final inputText = localObjectiveController.text.trim();

    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No local objectives text provided');
      return;
    }

    // Split by full stops and clean up
    final objectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (objectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid local objectives found');
      return;
    }

    // Capitalize first letter of each objective
    _localObjectives = objectives
        .map((obj) => obj.isEmpty ? obj : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();

    _currentStep = GoDetailsStep.afterLocalObjectives;

    Logger.info('GoDetailsProvider: Processed ${_localObjectives.length} local objectives: $_localObjectives');
    notifyListeners();
  }

  /// Toggles pathway creation for a specific LO
  void togglePathwayCreation(int loIndex) {
    _pathwayCreationStates[loIndex] = !(_pathwayCreationStates[loIndex] ?? false);

    // Clear selections if closing
    if (!_pathwayCreationStates[loIndex]!) {
      _pathwaySelectedRoles.remove(loIndex);
      _pathwaySelectedTypes.remove(loIndex);
      _pathwaySelectedLOs.remove(loIndex);
    }

    Logger.info('GoDetailsProvider: Toggled pathway creation for LO-${loIndex + 1}: ${_pathwayCreationStates[loIndex]}');
    notifyListeners();
  }

  /// Sets the selected role for pathway creation
  void setPathwaySelectedRole(int loIndex, PostgresRole? role) {
    _pathwaySelectedRoles[loIndex] = role;
    Logger.info('GoDetailsProvider: Set pathway role for LO-${loIndex + 1}: ${role?.name}');
    notifyListeners();
  }

  /// Sets the selected type for pathway creation
  void setPathwaySelectedType(int loIndex, String? type) {
    _pathwaySelectedTypes[loIndex] = type;

    // Clear LO selection if type changes
    if (type != 'Sequential') {
      _pathwaySelectedLOs.remove(loIndex);
    }

    Logger.info('GoDetailsProvider: Set pathway type for LO-${loIndex + 1}: $type');
    notifyListeners();
  }

  /// Sets the selected LO for sequential pathway
  void setPathwaySelectedLO(int loIndex, String? selectedLO) {
    _pathwaySelectedLOs[loIndex] = selectedLO;
    Logger.info('GoDetailsProvider: Set pathway LO for LO-${loIndex + 1}: $selectedLO');
    notifyListeners();
  }

  /// Toggles LO insertion text field for a specific LO
  void toggleLoInsertion(int loIndex) {
    final isCurrentlyOpen = _loInsertionStates[loIndex] ?? false;

    // Close all other insertion fields first
    _closeAllLoInsertions();

    if (!isCurrentlyOpen) {
      // Open insertion for this LO
      _loInsertionStates[loIndex] = true;
      _loInsertionControllers[loIndex] = TextEditingController();
    }

    Logger.info('GoDetailsProvider: Toggled LO insertion for LO-${loIndex + 1}: ${_loInsertionStates[loIndex]}');
    notifyListeners();
  }

  /// Closes all LO insertion text fields
  void _closeAllLoInsertions() {
    // Dispose controllers and clear states
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();
    _loInsertionStates.clear();
  }

  /// Processes and inserts new LOs after the specified index
  void processLoInsertion(int afterIndex) {
    final controller = _loInsertionControllers[afterIndex];
    if (controller == null) {
      Logger.warning('GoDetailsProvider: No controller found for LO insertion at index $afterIndex');
      return;
    }

    final inputText = controller.text.trim();
    if (inputText.isEmpty) {
      Logger.warning('GoDetailsProvider: No text provided for LO insertion');
      return;
    }

    // Split by dots and clean up
    final newObjectives = inputText
        .split('.')
        .map((obj) => obj.trim())
        .where((obj) => obj.isNotEmpty)
        .toList();

    if (newObjectives.isEmpty) {
      Logger.warning('GoDetailsProvider: No valid LOs found for insertion');
      return;
    }

    // Capitalize first letter of each objective
    final formattedObjectives = newObjectives
        .map((obj) => obj.isEmpty ? obj : obj[0].toUpperCase() + obj.substring(1).toLowerCase())
        .toList();

    // Insert new LOs after the specified index
    _insertLocalObjectives(afterIndex, formattedObjectives);

    // Close the insertion field
    _loInsertionStates[afterIndex] = false;
    controller.dispose();
    _loInsertionControllers.remove(afterIndex);

    Logger.info('GoDetailsProvider: Inserted ${formattedObjectives.length} LOs after index $afterIndex: $formattedObjectives');
    notifyListeners();
  }

  /// Inserts new local objectives at the specified position
  void _insertLocalObjectives(int afterIndex, List<String> newObjectives) {
    // Insert new objectives after the specified index
    for (int i = 0; i < newObjectives.length; i++) {
      _localObjectives.insert(afterIndex + 1 + i, newObjectives[i]);
    }

    // Update pathway creation states - shift indices for items after insertion point
    final updatedPathwayStates = <int, bool>{};
    final updatedPathwayRoles = <int, PostgresRole?>{};
    final updatedPathwayTypes = <int, String?>{};
    final updatedPathwayLOs = <int, String?>{};

    for (final entry in _pathwayCreationStates.entries) {
      final oldIndex = entry.key;
      final newIndex = oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayStates[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedRoles.entries) {
      final oldIndex = entry.key;
      final newIndex = oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayRoles[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedTypes.entries) {
      final oldIndex = entry.key;
      final newIndex = oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayTypes[newIndex] = entry.value;
    }

    for (final entry in _pathwaySelectedLOs.entries) {
      final oldIndex = entry.key;
      final newIndex = oldIndex > afterIndex ? oldIndex + newObjectives.length : oldIndex;
      updatedPathwayLOs[newIndex] = entry.value;
    }

    // Replace the maps with updated indices
    _pathwayCreationStates.clear();
    _pathwayCreationStates.addAll(updatedPathwayStates);

    _pathwaySelectedRoles.clear();
    _pathwaySelectedRoles.addAll(updatedPathwayRoles);

    _pathwaySelectedTypes.clear();
    _pathwaySelectedTypes.addAll(updatedPathwayTypes);

    _pathwaySelectedLOs.clear();
    _pathwaySelectedLOs.addAll(updatedPathwayLOs);
  }

  /// Resets to initial step
  void resetToInitial() {
    _currentStep = GoDetailsStep.initial;
    _generatedDescription = null;
    _localObjectives.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _setValidationError(null);
    Logger.info('GoDetailsProvider: Reset to initial step');
    notifyListeners();
  }

  /// Clears all form data
  void clearForm() {
    solutionController.clear();
    descriptionController.clear();
    localObjectiveController.clear();
    _selectedRole = null;
    resetToInitial();
    Logger.info('GoDetailsProvider: Form cleared');
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    solutionController.dispose();
    descriptionController.dispose();
    localObjectiveController.dispose();

    // Dispose LO insertion controllers
    for (final controller in _loInsertionControllers.values) {
      controller.dispose();
    }
    _loInsertionControllers.clear();

    super.dispose();
  }
}
