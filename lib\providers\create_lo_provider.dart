import 'package:flutter/material.dart';
import 'package:nsl/utils/logger.dart';

class CreateEntityProvider extends ChangeNotifier {
  final entityNameController = TextEditingController();

  bool _isValidating = false;
  String? _validationError;
  static List entityTypes = [
    "master",
    "reference",
    "transaction",
    "configuration",
    "intelligence",
    "aggregate",
    "contextual"
  ];
  String selectedEntityType = entityTypes[0];

  String? get validationError => _validationError;
  bool get isValidating => _isValidating;

  /// Validates the solution and moves to next step
  Future<void> validateSolution() async {
    if (entityNameController.text.trim().isEmpty) {
      return; // Just return without error, don't validate empty solution
    }

    _setValidating(true);
    _setValidationError(null);

    try {
      Logger.info(
          'GoDetailsProvider: Validating solution: ${entityNameController.text}');

      // Simulate API call delay
      await Future.delayed(const Duration(seconds: 1));

      Logger.info('CreateEntityProvider: Solution validated successfully');
    } catch (e) {
      Logger.error('CreateEntityProvider: Validation error - $e');
    } finally {
      _setValidating(false);
    }
  }

  updateEntityValue(value) {
    selectedEntityType = value;
    notifyListeners();
  }

  /// Private helper methods
  void _setValidating(bool validating) {
    if (_isValidating != validating) {
      _isValidating = validating;
      notifyListeners();
    }
  }

  void _setValidationError(String? error) {
    if (_validationError != error) {
      _validationError = error;
      notifyListeners();
    }
  }
}
